'use client'

import dynamic from 'next/dynamic'
import { useState, useEffect, useCallback, useMemo } from 'react'
import { commands } from '@uiw/react-md-editor'

// Dynamically import MDEditor to avoid SSR issues
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor'),
  {
    ssr: false,
    loading: () => (
      <div className="w-full p-4 border border-input bg-background text-foreground rounded-lg animate-pulse min-h-[400px] flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce"></div>
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          <span className="text-muted-foreground ml-2">Loading editor...</span>
        </div>
      </div>
    )
  }
)

interface CherryEditorProps {
  value?: string
  onChange?: (value: string) => void
  minHeight?: string
  placeholder?: string
  autoFocus?: boolean
  enablePreview?: boolean
  enableFullscreen?: boolean
  showWordCount?: boolean
  className?: string
}

export function CherryEditor({
  value = '',
  onChange,
  minHeight = '500px',
  placeholder = 'Start writing your amazing content...',
  autoFocus = false,
  enablePreview = true,
  enableFullscreen = true,
  showWordCount = true,
  className = ''
}: CherryEditorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Word count calculation
  const wordCount = useMemo(() => {
    if (!showWordCount || !value) return null
    const words = value.trim().split(/\s+/).filter(word => word.length > 0).length
    const characters = value.length
    const charactersNoSpaces = value.replace(/\s/g, '').length
    return { words, characters, charactersNoSpaces }
  }, [value, showWordCount])

  // Handle change with debouncing for performance
  const handleChange = useCallback((val?: string) => {
    onChange?.(val || '')
  }, [onChange])

  // Custom commands configuration
  const customCommands = useMemo(() => {
    const baseCommands = [
      commands.bold,
      commands.italic,
      commands.strikethrough,
      commands.hr,
      commands.group([
        commands.title1,
        commands.title2,
        commands.title3,
        commands.title4,
        commands.title5,
        commands.title6,
      ], {
        name: 'title',
        groupName: 'title',
        buttonProps: { 'aria-label': 'Insert title' }
      }),
      commands.divider,
      commands.link,
      commands.quote,
      commands.code,
      commands.codeBlock,
      commands.divider,
      commands.unorderedListCommand,
      commands.orderedListCommand,
      commands.checkedListCommand,
      commands.divider,
      commands.table,
      commands.image,
    ]

    if (enablePreview) {
      baseCommands.push(commands.divider, commands.codeEdit, commands.codeLive, commands.codePreview)
    }

    return baseCommands
  }, [enablePreview])

  const extraCommands = useMemo(() => {
    const extras = []
    if (enableFullscreen) {
      extras.push(commands.fullscreen)
    }
    return extras
  }, [enableFullscreen])

  // Show loading state during SSR
  if (!mounted) {
    return (
      <div
        className={`w-full p-4 border border-input bg-background text-foreground rounded-lg animate-pulse flex items-center justify-center ${className}`}
        style={{ minHeight }}
      >
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce"></div>
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-4 h-4 bg-primary/20 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          <span className="text-muted-foreground ml-2">Loading editor...</span>
        </div>
      </div>
    )
  }

  try {
    return (
      <div className={`w-full enhanced-md-editor-wrapper ${className}`}>
        <div
          className="md-editor-container"
          style={{ minHeight }}
          data-color-mode="auto"
        >
          <MDEditor
            value={value}
            onChange={handleChange}
            preview="live"
            hideToolbar={false}
            visibleDragbar={false}
            autoFocus={autoFocus}
            commands={customCommands}
            extraCommands={extraCommands}
            textareaProps={{
              placeholder,
              'aria-label': 'Markdown editor',
              style: {
                fontSize: 14,
                lineHeight: 1.6,
                fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                color: 'inherit'
              }
            }}
            style={{
              backgroundColor: 'hsl(var(--background))',
              color: 'hsl(var(--foreground))',
              minHeight: minHeight,
              zIndex: 9999 // Ensure editor content is visible above other elements in fullscreen
            }}
            previewOptions={{
              style: {
                backgroundColor: 'hsl(var(--background))',
                color: 'hsl(var(--foreground))'
              }
            }}
          />
        </div>

        {/* Word count display */}
        {showWordCount && wordCount && (
          <div className="flex justify-between items-center px-3 py-2 text-xs text-muted-foreground bg-muted/30 border-t border-border rounded-b-lg">
            <div className="flex space-x-4">
              <span>{wordCount.words} words</span>
              <span>{wordCount.characters} characters</span>
              <span>{wordCount.charactersNoSpaces} characters (no spaces)</span>
            </div>
            <div className="text-xs opacity-70">
              Markdown supported
            </div>
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error('Error rendering MDEditor:', error)
    return (
      <div className={`w-full p-4 border border-input bg-background text-foreground rounded-lg ${className}`}>
        <div className="text-destructive mb-2 flex items-center">
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Error loading editor
        </div>
        <textarea
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          className="w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground focus:ring-2 focus:ring-primary/20 rounded-md p-2"
          style={{ minHeight: `calc(${minHeight} - 4rem)` }}
          aria-label="Fallback markdown editor"
        />
        {showWordCount && wordCount && (
          <div className="flex justify-between items-center px-3 py-2 text-xs text-muted-foreground bg-muted/30 border-t border-border rounded-b-lg mt-2">
            <div className="flex space-x-4">
              <span>{wordCount.words} words</span>
              <span>{wordCount.characters} characters</span>
            </div>
          </div>
        )}
      </div>
    )
  }
}
