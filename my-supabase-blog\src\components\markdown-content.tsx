'use client'

import { useMemo, useEffect } from 'react'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import '@uiw/react-markdown-preview/markdown.css'
import 'highlight.js/styles/github-dark.css'

interface MarkdownContentProps {
  content: string
  className?: string
  enableHtml?: boolean
  enableBreaks?: boolean
  enableTypographer?: boolean
}

export default function MarkdownContent({
  content,
  className = '',
  enableHtml = true,
  enableBreaks = false,
  enableTypographer = true
}: MarkdownContentProps) {
  const processedContent = useMemo(() => {
    // Initialize markdown-it with options
    const md = new MarkdownIt({
      html: enableHtml,        // Enable HTML tags in source
      linkify: true,          // Autoconvert URL-like text to links
      typographer: enableTypographer, // Enable some language-neutral replacement + quotes beautification
      breaks: enableBreaks    // Convert '\n' in paragraphs into <br>
    })

    // Render markdown to HTML
    return md.render(content)
  }, [content, enableHtml, enableBreaks, enableTypographer])

  // Initialize highlight.js after content is rendered
  useEffect(() => {
    hljs.highlightAll()
  }, [processedContent])

  return (
    <div
      className={`prose prose-base sm:prose-lg max-w-none dark:prose-invert markdown-renderer enhanced-markdown-content ${className}`}
      data-color-mode="auto"
      dangerouslySetInnerHTML={{ __html: processedContent }}
      style={{
        backgroundColor: 'transparent',
        color: 'inherit'
      }}
    />
  )
}
