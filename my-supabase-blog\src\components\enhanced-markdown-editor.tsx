'use client'

import { useState, useCallback, useMemo, useEffect, useRef } from 'react'
import { CherryEditor } from './cherry-editor'
import { useAutoSave } from '@/hooks/use-auto-save'

interface EnhancedMarkdownEditorProps {
  value?: string
  onChange?: (value: string) => void
  onAutoSave?: (value: string) => void | Promise<void>
  minHeight?: string
  placeholder?: string
  autoFocus?: boolean
  enablePreview?: boolean
  enableFullscreen?: boolean
  showWordCount?: boolean
  enableAutoSave?: boolean
  autoSaveDelay?: number
  className?: string
  label?: string
  required?: boolean
  error?: string
}

export function EnhancedMarkdownEditor({
  value = '',
  onChange,
  onAutoSave,
  minHeight = '500px',
  placeholder = 'Start writing your amazing content...',
  autoFocus = false,
  enablePreview = true,
  enableFullscreen = true,
  showWordCount = true,
  enableAutoSave = false,
  autoSaveDelay = 3000,
  className = '',
  label,
  required = false,
  error
}: EnhancedMarkdownEditorProps) {
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const editorRef = useRef<HTMLDivElement>(null)

  // Auto-save functionality
  const handleAutoSave = useCallback(async (data: string) => {
    if (onAutoSave) {
      await onAutoSave(data)
      setLastSaved(new Date())
    }
  }, [onAutoSave])

  const { isSaving, forceSave } = useAutoSave(value, {
    delay: autoSaveDelay,
    enabled: enableAutoSave && !!onAutoSave,
    onSave: handleAutoSave
  })

  // Format last saved time
  const lastSavedText = useMemo(() => {
    if (!lastSaved) return null
    const now = new Date()
    const diff = now.getTime() - lastSaved.getTime()
    
    if (diff < 60000) { // Less than 1 minute
      return 'Saved just now'
    } else if (diff < 3600000) { // Less than 1 hour
      const minutes = Math.floor(diff / 60000)
      return `Saved ${minutes} minute${minutes > 1 ? 's' : ''} ago`
    } else {
      return `Saved at ${lastSaved.toLocaleTimeString()}`
    }
  }, [lastSaved])

  const handleChange = useCallback((newValue: string) => {
    onChange?.(newValue)
  }, [onChange])

  const handleForceSave = useCallback(() => {
    if (enableAutoSave && onAutoSave) {
      forceSave()
    }
  }, [enableAutoSave, onAutoSave, forceSave])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S for force save
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        if (enableAutoSave && onAutoSave) {
          handleForceSave()
        }
      }

      // Escape to exit fullscreen (if implemented)
      if (event.key === 'Escape') {
        // This would be handled by the MDEditor component
      }
    }

    const editorElement = editorRef.current
    if (editorElement) {
      editorElement.addEventListener('keydown', handleKeyDown)
      return () => {
        editorElement.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [enableAutoSave, onAutoSave, handleForceSave])

  return (
    <div
      ref={editorRef}
      className={`enhanced-markdown-editor ${className}`}
      role="region"
      aria-label={label || "Markdown Editor"}
    >
      {/* Label and controls */}
      {(label || enableAutoSave) && (
        <div className="flex items-center justify-between mb-3">
          {label && (
            <label className="flex items-center text-sm font-semibold text-foreground">
              <div className="w-6 h-6 bg-primary/10 rounded-lg flex items-center justify-center mr-2">
                <svg className="w-3 h-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </label>
          )}
          
          {enableAutoSave && (
            <div className="flex items-center space-x-3">
              {/* Auto-save status */}
              <div className="flex items-center text-xs text-muted-foreground">
                {isSaving ? (
                  <>
                    <div className="w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2"></div>
                    Saving...
                  </>
                ) : lastSavedText ? (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    {lastSavedText}
                  </>
                ) : (
                  <>
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    Not saved
                  </>
                )}
              </div>
              
              {/* Manual save button */}
              <button
                type="button"
                onClick={handleForceSave}
                disabled={isSaving}
                className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors disabled:opacity-50"
              >
                Save Now
              </button>
            </div>
          )}
        </div>
      )}

      {/* Editor */}
      <CherryEditor
        value={value}
        onChange={handleChange}
        minHeight={minHeight}
        placeholder={placeholder}
        autoFocus={autoFocus}
        enablePreview={enablePreview}
        enableFullscreen={enableFullscreen}
        showWordCount={showWordCount}
        className="shadow-sm rounded-xl"
      />

      {/* Error message */}
      {error && (
        <div className="mt-2 text-sm text-destructive flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </div>
      )}

      {/* Keyboard shortcuts help */}
      <div className="mt-2 text-xs text-muted-foreground">
        <details className="cursor-pointer">
          <summary className="hover:text-foreground transition-colors">Keyboard Shortcuts</summary>
          <div className="mt-2 space-y-1 pl-4 border-l-2 border-border">
            <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+B</kbd> Bold</div>
            <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+I</kbd> Italic</div>
            <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd> Link</div>
            <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+Shift+C</kbd> Code block</div>
            {enableAutoSave && (
              <div><kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+S</kbd> Force save</div>
            )}
          </div>
        </details>
      </div>
    </div>
  )
}
