{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/cherry-editor.tsx"], "sourcesContent": ["'use client'\n\nimport dynamic from 'next/dynamic'\nimport { useState, useEffect, useCallback, useMemo } from 'react'\nimport { commands } from '@uiw/react-md-editor'\n\n// Dynamically import MDEditor to avoid SSR issues\nconst MDEditor = dynamic(\n  () => import('@uiw/react-md-editor'),\n  {\n    ssr: false,\n    loading: () => (\n      <div className=\"w-full p-4 border border-input bg-background text-foreground rounded-lg animate-pulse min-h-[400px] flex items-center justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\"></div>\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n          <span className=\"text-muted-foreground ml-2\">Loading editor...</span>\n        </div>\n      </div>\n    )\n  }\n)\n\ninterface CherryEditorProps {\n  value?: string\n  onChange?: (value: string) => void\n  minHeight?: string\n  placeholder?: string\n  autoFocus?: boolean\n  enablePreview?: boolean\n  enableFullscreen?: boolean\n  showWordCount?: boolean\n  className?: string\n}\n\nexport function CherryEditor({\n  value = '',\n  onChange,\n  minHeight = '500px',\n  placeholder = 'Start writing your amazing content...',\n  autoFocus = false,\n  enablePreview = true,\n  enableFullscreen = true,\n  showWordCount = true,\n  className = ''\n}: CherryEditorProps) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Word count calculation\n  const wordCount = useMemo(() => {\n    if (!showWordCount || !value) return null\n    const words = value.trim().split(/\\s+/).filter(word => word.length > 0).length\n    const characters = value.length\n    const charactersNoSpaces = value.replace(/\\s/g, '').length\n    return { words, characters, charactersNoSpaces }\n  }, [value, showWordCount])\n\n  // Handle change with debouncing for performance\n  const handleChange = useCallback((val?: string) => {\n    onChange?.(val || '')\n  }, [onChange])\n\n  // Custom commands configuration\n  const customCommands = useMemo(() => {\n    const baseCommands = [\n      commands.bold,\n      commands.italic,\n      commands.strikethrough,\n      commands.hr,\n      commands.group([\n        commands.title1,\n        commands.title2,\n        commands.title3,\n        commands.title4,\n        commands.title5,\n        commands.title6,\n      ], {\n        name: 'title',\n        groupName: 'title',\n        buttonProps: { 'aria-label': 'Insert title' }\n      }),\n      commands.divider,\n      commands.link,\n      commands.quote,\n      commands.code,\n      commands.codeBlock,\n      commands.divider,\n      commands.unorderedListCommand,\n      commands.orderedListCommand,\n      commands.checkedListCommand,\n      commands.divider,\n      commands.table,\n      commands.image,\n    ]\n\n    if (enablePreview) {\n      baseCommands.push(commands.divider, commands.codeEdit, commands.codeLive, commands.codePreview)\n    }\n\n    return baseCommands\n  }, [enablePreview])\n\n  const extraCommands = useMemo(() => {\n    const extras = []\n    if (enableFullscreen) {\n      extras.push(commands.fullscreen)\n    }\n    return extras\n  }, [enableFullscreen])\n\n  // Show loading state during SSR\n  if (!mounted) {\n    return (\n      <div\n        className={`w-full p-4 border border-input bg-background text-foreground rounded-lg animate-pulse flex items-center justify-center ${className}`}\n        style={{ minHeight }}\n      >\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\"></div>\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"w-4 h-4 bg-primary/20 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n          <span className=\"text-muted-foreground ml-2\">Loading editor...</span>\n        </div>\n      </div>\n    )\n  }\n\n  try {\n    return (\n      <div className={`w-full enhanced-md-editor-wrapper ${className}`}>\n        <div\n          className=\"md-editor-container\"\n          style={{ minHeight }}\n          data-color-mode=\"auto\"\n        >\n          <MDEditor\n            value={value}\n            onChange={handleChange}\n            preview=\"live\"\n            hideToolbar={false}\n            visibleDragbar={false}\n            autoFocus={autoFocus}\n            commands={customCommands}\n            extraCommands={extraCommands}\n            textareaProps={{\n              placeholder,\n              'aria-label': 'Markdown editor',\n              style: {\n                fontSize: 14,\n                lineHeight: 1.6,\n                fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace',\n                color: 'inherit'\n              }\n            }}\n            style={{\n              backgroundColor: 'hsl(var(--background))',\n              color: 'hsl(var(--foreground))',\n              minHeight: minHeight\n            }}\n            previewOptions={{\n              style: {\n                backgroundColor: 'hsl(var(--background))',\n                color: 'hsl(var(--foreground))'\n              }\n            }}\n          />\n        </div>\n\n        {/* Word count display */}\n        {showWordCount && wordCount && (\n          <div className=\"flex justify-between items-center px-3 py-2 text-xs text-muted-foreground bg-muted/30 border-t border-border rounded-b-lg\">\n            <div className=\"flex space-x-4\">\n              <span>{wordCount.words} words</span>\n              <span>{wordCount.characters} characters</span>\n              <span>{wordCount.charactersNoSpaces} characters (no spaces)</span>\n            </div>\n            <div className=\"text-xs opacity-70\">\n              Markdown supported\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  } catch (error) {\n    console.error('Error rendering MDEditor:', error)\n    return (\n      <div className={`w-full p-4 border border-input bg-background text-foreground rounded-lg ${className}`}>\n        <div className=\"text-destructive mb-2 flex items-center\">\n          <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          Error loading editor\n        </div>\n        <textarea\n          value={value}\n          onChange={(e) => onChange?.(e.target.value)}\n          placeholder={placeholder}\n          className=\"w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground focus:ring-2 focus:ring-primary/20 rounded-md p-2\"\n          style={{ minHeight: `calc(${minHeight} - 4rem)` }}\n          aria-label=\"Fallback markdown editor\"\n        />\n        {showWordCount && wordCount && (\n          <div className=\"flex justify-between items-center px-3 py-2 text-xs text-muted-foreground bg-muted/30 border-t border-border rounded-b-lg mt-2\">\n            <div className=\"flex space-x-4\">\n              <span>{wordCount.words} words</span>\n              <span>{wordCount.characters} characters</span>\n            </div>\n          </div>\n        )}\n      </div>\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;;AAJA;;;;AAMA,kDAAkD;AAClD,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACrB;;;;;;IAEE,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAoD,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;kCACnG,6LAAC;wBAAI,WAAU;wBAAoD,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;kCACnG,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;;;;;;;KAVjD;AA6BC,SAAS,aAAa,EAC3B,QAAQ,EAAE,EACV,QAAQ,EACR,YAAY,OAAO,EACnB,cAAc,uCAAuC,EACrD,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,mBAAmB,IAAI,EACvB,gBAAgB,IAAI,EACpB,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,WAAW;QACb;iCAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YACrC,MAAM,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;mDAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;kDAAG,MAAM;YAC9E,MAAM,aAAa,MAAM,MAAM;YAC/B,MAAM,qBAAqB,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM;YAC1D,OAAO;gBAAE;gBAAO;gBAAY;YAAmB;QACjD;0CAAG;QAAC;QAAO;KAAc;IAEzB,gDAAgD;IAChD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAChC,WAAW,OAAO;QACpB;iDAAG;QAAC;KAAS;IAEb,gCAAgC;IAChC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAC7B,MAAM,eAAe;gBACnB,iNAAA,CAAA,WAAQ,CAAC,IAAI;gBACb,iNAAA,CAAA,WAAQ,CAAC,MAAM;gBACf,iNAAA,CAAA,WAAQ,CAAC,aAAa;gBACtB,iNAAA,CAAA,WAAQ,CAAC,EAAE;gBACX,iNAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;oBACb,iNAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iNAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iNAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iNAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iNAAA,CAAA,WAAQ,CAAC,MAAM;oBACf,iNAAA,CAAA,WAAQ,CAAC,MAAM;iBAChB,EAAE;oBACD,MAAM;oBACN,WAAW;oBACX,aAAa;wBAAE,cAAc;oBAAe;gBAC9C;gBACA,iNAAA,CAAA,WAAQ,CAAC,OAAO;gBAChB,iNAAA,CAAA,WAAQ,CAAC,IAAI;gBACb,iNAAA,CAAA,WAAQ,CAAC,KAAK;gBACd,iNAAA,CAAA,WAAQ,CAAC,IAAI;gBACb,iNAAA,CAAA,WAAQ,CAAC,SAAS;gBAClB,iNAAA,CAAA,WAAQ,CAAC,OAAO;gBAChB,iNAAA,CAAA,WAAQ,CAAC,oBAAoB;gBAC7B,iNAAA,CAAA,WAAQ,CAAC,kBAAkB;gBAC3B,iNAAA,CAAA,WAAQ,CAAC,kBAAkB;gBAC3B,iNAAA,CAAA,WAAQ,CAAC,OAAO;gBAChB,iNAAA,CAAA,WAAQ,CAAC,KAAK;gBACd,iNAAA,CAAA,WAAQ,CAAC,KAAK;aACf;YAED,IAAI,eAAe;gBACjB,aAAa,IAAI,CAAC,iNAAA,CAAA,WAAQ,CAAC,OAAO,EAAE,iNAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE,iNAAA,CAAA,WAAQ,CAAC,QAAQ,EAAE,iNAAA,CAAA,WAAQ,CAAC,WAAW;YAChG;YAEA,OAAO;QACT;+CAAG;QAAC;KAAc;IAElB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC5B,MAAM,SAAS,EAAE;YACjB,IAAI,kBAAkB;gBACpB,OAAO,IAAI,CAAC,iNAAA,CAAA,WAAQ,CAAC,UAAU;YACjC;YACA,OAAO;QACT;8CAAG;QAAC;KAAiB;IAErB,gCAAgC;IAChC,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YACC,WAAW,CAAC,uHAAuH,EAAE,WAAW;YAChJ,OAAO;gBAAE;YAAU;sBAEnB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAoD,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;kCACnG,6LAAC;wBAAI,WAAU;wBAAoD,OAAO;4BAAE,gBAAgB;wBAAO;;;;;;kCACnG,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIrD;IAEA,IAAI;QACF,qBACE,6LAAC;YAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;;8BAC9D,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE;oBAAU;oBACnB,mBAAgB;8BAEhB,cAAA,6LAAC;wBACC,OAAO;wBACP,UAAU;wBACV,SAAQ;wBACR,aAAa;wBACb,gBAAgB;wBAChB,WAAW;wBACX,UAAU;wBACV,eAAe;wBACf,eAAe;4BACb;4BACA,cAAc;4BACd,OAAO;gCACL,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,OAAO;4BACT;wBACF;wBACA,OAAO;4BACL,iBAAiB;4BACjB,OAAO;4BACP,WAAW;wBACb;wBACA,gBAAgB;4BACd,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;wBACF;;;;;;;;;;;gBAKH,iBAAiB,2BAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAM,UAAU,KAAK;wCAAC;;;;;;;8CACvB,6LAAC;;wCAAM,UAAU,UAAU;wCAAC;;;;;;;8CAC5B,6LAAC;;wCAAM,UAAU,kBAAkB;wCAAC;;;;;;;;;;;;;sCAEtC,6LAAC;4BAAI,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;IAO9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,qBACE,6LAAC;YAAI,WAAW,CAAC,wEAAwE,EAAE,WAAW;;8BACpG,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACtE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;wBACjE;;;;;;;8BAGR,6LAAC;oBACC,OAAO;oBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oBAC1C,aAAa;oBACb,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,KAAK,EAAE,UAAU,QAAQ,CAAC;oBAAC;oBAChD,cAAW;;;;;;gBAEZ,iBAAiB,2BAChB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAM,UAAU,KAAK;oCAAC;;;;;;;0CACvB,6LAAC;;oCAAM,UAAU,UAAU;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;IAMxC;AACF;GArLgB;MAAA", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/hooks/use-auto-save.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useCallback } from 'react'\n\ninterface UseAutoSaveOptions {\n  delay?: number\n  enabled?: boolean\n  onSave?: (data: any) => void | Promise<void>\n}\n\nexport function useAutoSave<T>(\n  data: T,\n  options: UseAutoSaveOptions = {}\n) {\n  const {\n    delay = 2000, // 2 seconds default\n    enabled = true,\n    onSave\n  } = options\n\n  const timeoutRef = useRef<NodeJS.Timeout>()\n  const lastSavedRef = useRef<T>(data)\n  const isSavingRef = useRef(false)\n\n  const save = useCallback(async () => {\n    if (!onSave || isSavingRef.current) return\n\n    try {\n      isSavingRef.current = true\n      await onSave(data)\n      lastSavedRef.current = data\n    } catch (error) {\n      console.error('Auto-save failed:', error)\n    } finally {\n      isSavingRef.current = false\n    }\n  }, [data, onSave])\n\n  useEffect(() => {\n    if (!enabled || !onSave) return\n\n    // Clear existing timeout\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n    }\n\n    // Check if data has changed\n    const hasChanged = JSON.stringify(data) !== JSON.stringify(lastSavedRef.current)\n    \n    if (hasChanged && !isSavingRef.current) {\n      timeoutRef.current = setTimeout(save, delay)\n    }\n\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current)\n      }\n    }\n  }, [data, delay, enabled, save])\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current)\n      }\n    }\n  }, [])\n\n  return {\n    isSaving: isSavingRef.current,\n    forceSave: save\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAUO,SAAS,YACd,IAAO,EACP,UAA8B,CAAC,CAAC;;IAEhC,MAAM,EACJ,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,MAAM,EACP,GAAG;IAEJ,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAK;IAC/B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE;YACvB,IAAI,CAAC,UAAU,YAAY,OAAO,EAAE;YAEpC,IAAI;gBACF,YAAY,OAAO,GAAG;gBACtB,MAAM,OAAO;gBACb,aAAa,OAAO,GAAG;YACzB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC,SAAU;gBACR,YAAY,OAAO,GAAG;YACxB;QACF;wCAAG;QAAC;QAAM;KAAO;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,WAAW,CAAC,QAAQ;YAEzB,yBAAyB;YACzB,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;YAEA,4BAA4B;YAC5B,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,aAAa,OAAO;YAE/E,IAAI,cAAc,CAAC,YAAY,OAAO,EAAE;gBACtC,WAAW,OAAO,GAAG,WAAW,MAAM;YACxC;YAEA;yCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;gCAAG;QAAC;QAAM;QAAO;QAAS;KAAK;IAE/B,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;yCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,aAAa,WAAW,OAAO;oBACjC;gBACF;;QACF;gCAAG,EAAE;IAEL,OAAO;QACL,UAAU,YAAY,OAAO;QAC7B,WAAW;IACb;AACF;GA/DgB", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/enhanced-markdown-editor.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useMemo, useEffect, useRef } from 'react'\nimport { CherryEditor } from './cherry-editor'\nimport { useAutoSave } from '@/hooks/use-auto-save'\n\ninterface EnhancedMarkdownEditorProps {\n  value?: string\n  onChange?: (value: string) => void\n  onAutoSave?: (value: string) => void | Promise<void>\n  minHeight?: string\n  placeholder?: string\n  autoFocus?: boolean\n  enablePreview?: boolean\n  enableFullscreen?: boolean\n  showWordCount?: boolean\n  enableAutoSave?: boolean\n  autoSaveDelay?: number\n  className?: string\n  label?: string\n  required?: boolean\n  error?: string\n}\n\nexport function EnhancedMarkdownEditor({\n  value = '',\n  onChange,\n  onAutoSave,\n  minHeight = '500px',\n  placeholder = 'Start writing your amazing content...',\n  autoFocus = false,\n  enablePreview = true,\n  enableFullscreen = true,\n  showWordCount = true,\n  enableAutoSave = false,\n  autoSaveDelay = 3000,\n  className = '',\n  label,\n  required = false,\n  error\n}: EnhancedMarkdownEditorProps) {\n  const [lastSaved, setLastSaved] = useState<Date | null>(null)\n  const editorRef = useRef<HTMLDivElement>(null)\n\n  // Auto-save functionality\n  const handleAutoSave = useCallback(async (data: string) => {\n    if (onAutoSave) {\n      await onAutoSave(data)\n      setLastSaved(new Date())\n    }\n  }, [onAutoSave])\n\n  const { isSaving, forceSave } = useAutoSave(value, {\n    delay: autoSaveDelay,\n    enabled: enableAutoSave && !!onAutoSave,\n    onSave: handleAutoSave\n  })\n\n  // Format last saved time\n  const lastSavedText = useMemo(() => {\n    if (!lastSaved) return null\n    const now = new Date()\n    const diff = now.getTime() - lastSaved.getTime()\n    \n    if (diff < 60000) { // Less than 1 minute\n      return 'Saved just now'\n    } else if (diff < 3600000) { // Less than 1 hour\n      const minutes = Math.floor(diff / 60000)\n      return `Saved ${minutes} minute${minutes > 1 ? 's' : ''} ago`\n    } else {\n      return `Saved at ${lastSaved.toLocaleTimeString()}`\n    }\n  }, [lastSaved])\n\n  const handleChange = useCallback((newValue: string) => {\n    onChange?.(newValue)\n  }, [onChange])\n\n  const handleForceSave = useCallback(() => {\n    if (enableAutoSave && onAutoSave) {\n      forceSave()\n    }\n  }, [enableAutoSave, onAutoSave, forceSave])\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Ctrl+S for force save\n      if (event.ctrlKey && event.key === 's') {\n        event.preventDefault()\n        if (enableAutoSave && onAutoSave) {\n          handleForceSave()\n        }\n      }\n\n      // Escape to exit fullscreen (if implemented)\n      if (event.key === 'Escape') {\n        // This would be handled by the MDEditor component\n      }\n    }\n\n    const editorElement = editorRef.current\n    if (editorElement) {\n      editorElement.addEventListener('keydown', handleKeyDown)\n      return () => {\n        editorElement.removeEventListener('keydown', handleKeyDown)\n      }\n    }\n  }, [enableAutoSave, onAutoSave, handleForceSave])\n\n  return (\n    <div\n      ref={editorRef}\n      className={`enhanced-markdown-editor ${className}`}\n      role=\"region\"\n      aria-label={label || \"Markdown Editor\"}\n    >\n      {/* Label and controls */}\n      {(label || enableAutoSave) && (\n        <div className=\"flex items-center justify-between mb-3\">\n          {label && (\n            <label className=\"flex items-center text-sm font-semibold text-foreground\">\n              <div className=\"w-6 h-6 bg-primary/10 rounded-lg flex items-center justify-center mr-2\">\n                <svg className=\"w-3 h-3 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                </svg>\n              </div>\n              {label}\n              {required && <span className=\"text-destructive ml-1\">*</span>}\n            </label>\n          )}\n          \n          {enableAutoSave && (\n            <div className=\"flex items-center space-x-3\">\n              {/* Auto-save status */}\n              <div className=\"flex items-center text-xs text-muted-foreground\">\n                {isSaving ? (\n                  <>\n                    <div className=\"w-3 h-3 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2\"></div>\n                    Saving...\n                  </>\n                ) : lastSavedText ? (\n                  <>\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></div>\n                    {lastSavedText}\n                  </>\n                ) : (\n                  <>\n                    <div className=\"w-2 h-2 bg-yellow-500 rounded-full mr-2\"></div>\n                    Not saved\n                  </>\n                )}\n              </div>\n              \n              {/* Manual save button */}\n              <button\n                type=\"button\"\n                onClick={handleForceSave}\n                disabled={isSaving}\n                className=\"text-xs px-2 py-1 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors disabled:opacity-50\"\n              >\n                Save Now\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Editor */}\n      <CherryEditor\n        value={value}\n        onChange={handleChange}\n        minHeight={minHeight}\n        placeholder={placeholder}\n        autoFocus={autoFocus}\n        enablePreview={enablePreview}\n        enableFullscreen={enableFullscreen}\n        showWordCount={showWordCount}\n        className=\"shadow-sm rounded-xl\"\n      />\n\n      {/* Error message */}\n      {error && (\n        <div className=\"mt-2 text-sm text-destructive flex items-center\">\n          <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          {error}\n        </div>\n      )}\n\n      {/* Keyboard shortcuts help */}\n      <div className=\"mt-2 text-xs text-muted-foreground\">\n        <details className=\"cursor-pointer\">\n          <summary className=\"hover:text-foreground transition-colors\">Keyboard Shortcuts</summary>\n          <div className=\"mt-2 space-y-1 pl-4 border-l-2 border-border\">\n            <div><kbd className=\"px-1 py-0.5 bg-muted rounded text-xs\">Ctrl+B</kbd> Bold</div>\n            <div><kbd className=\"px-1 py-0.5 bg-muted rounded text-xs\">Ctrl+I</kbd> Italic</div>\n            <div><kbd className=\"px-1 py-0.5 bg-muted rounded text-xs\">Ctrl+K</kbd> Link</div>\n            <div><kbd className=\"px-1 py-0.5 bg-muted rounded text-xs\">Ctrl+Shift+C</kbd> Code block</div>\n            {enableAutoSave && (\n              <div><kbd className=\"px-1 py-0.5 bg-muted rounded text-xs\">Ctrl+S</kbd> Force save</div>\n            )}\n          </div>\n        </details>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBO,SAAS,uBAAuB,EACrC,QAAQ,EAAE,EACV,QAAQ,EACR,UAAU,EACV,YAAY,OAAO,EACnB,cAAc,uCAAuC,EACrD,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,mBAAmB,IAAI,EACvB,gBAAgB,IAAI,EACpB,iBAAiB,KAAK,EACtB,gBAAgB,IAAI,EACpB,YAAY,EAAE,EACd,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACuB;;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,0BAA0B;IAC1B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OAAO;YACxC,IAAI,YAAY;gBACd,MAAM,WAAW;gBACjB,aAAa,IAAI;YACnB;QACF;6DAAG;QAAC;KAAW;IAEf,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjD,OAAO;QACP,SAAS,kBAAkB,CAAC,CAAC;QAC7B,QAAQ;IACV;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE;YAC5B,IAAI,CAAC,WAAW,OAAO;YACvB,MAAM,MAAM,IAAI;YAChB,MAAM,OAAO,IAAI,OAAO,KAAK,UAAU,OAAO;YAE9C,IAAI,OAAO,OAAO;gBAChB,OAAO;YACT,OAAO,IAAI,OAAO,SAAS;gBACzB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;gBAClC,OAAO,CAAC,MAAM,EAAE,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;YAC/D,OAAO;gBACL,OAAO,CAAC,SAAS,EAAE,UAAU,kBAAkB,IAAI;YACrD;QACF;wDAAG;QAAC;KAAU;IAEd,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAChC,WAAW;QACb;2DAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YAClC,IAAI,kBAAkB,YAAY;gBAChC;YACF;QACF;8DAAG;QAAC;QAAgB;QAAY;KAAU;IAE1C,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;kEAAgB,CAAC;oBACrB,wBAAwB;oBACxB,IAAI,MAAM,OAAO,IAAI,MAAM,GAAG,KAAK,KAAK;wBACtC,MAAM,cAAc;wBACpB,IAAI,kBAAkB,YAAY;4BAChC;wBACF;oBACF;oBAEA,6CAA6C;oBAC7C,IAAI,MAAM,GAAG,KAAK,UAAU;oBAC1B,kDAAkD;oBACpD;gBACF;;YAEA,MAAM,gBAAgB,UAAU,OAAO;YACvC,IAAI,eAAe;gBACjB,cAAc,gBAAgB,CAAC,WAAW;gBAC1C;wDAAO;wBACL,cAAc,mBAAmB,CAAC,WAAW;oBAC/C;;YACF;QACF;2CAAG;QAAC;QAAgB;QAAY;KAAgB;IAEhD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,yBAAyB,EAAE,WAAW;QAClD,MAAK;QACL,cAAY,SAAS;;YAGpB,CAAC,SAAS,cAAc,mBACvB,6LAAC;gBAAI,WAAU;;oBACZ,uBACC,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC9E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;4BAGxE;4BACA,0BAAY,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;oBAIxD,gCACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,yBACC;;sDACE,6LAAC;4CAAI,WAAU;;;;;;wCAA4F;;mDAG3G,8BACF;;sDACE,6LAAC;4CAAI,WAAU;;;;;;wCACd;;iEAGH;;sDACE,6LAAC;4CAAI,WAAU;;;;;;wCAAgD;;;;;;;;0CAOrE,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAST,6LAAC,yIAAA,CAAA,eAAY;gBACX,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,aAAa;gBACb,WAAW;gBACX,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,WAAU;;;;;;YAIX,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAQ,WAAU;sCAA0C;;;;;;sCAC7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAI,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;wCAAY;;;;;;;8CACvE,6LAAC;;sDAAI,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;wCAAY;;;;;;;8CACvE,6LAAC;;sDAAI,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;wCAAY;;;;;;;8CACvE,6LAAC;;sDAAI,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;wCAAkB;;;;;;;gCAC5E,gCACC,6LAAC;;sDAAI,6LAAC;4CAAI,WAAU;sDAAuC;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrF;GAxLgB;;QA4BkB,sIAAA,CAAA,cAAW;;;KA5B7B", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/components/markdown-content.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useEffect } from 'react'\nimport MarkdownIt from 'markdown-it'\nimport hljs from 'highlight.js'\nimport '@uiw/react-markdown-preview/markdown.css'\nimport 'highlight.js/styles/github-dark.css'\n\ninterface MarkdownContentProps {\n  content: string\n  className?: string\n  enableHtml?: boolean\n  enableBreaks?: boolean\n  enableTypographer?: boolean\n}\n\nexport default function MarkdownContent({\n  content,\n  className = '',\n  enableHtml = true,\n  enableBreaks = false,\n  enableTypographer = true\n}: MarkdownContentProps) {\n  const processedContent = useMemo(() => {\n    // Initialize markdown-it with options\n    const md = new MarkdownIt({\n      html: enableHtml,        // Enable HTML tags in source\n      linkify: true,          // Autoconvert URL-like text to links\n      typographer: enableTypographer, // Enable some language-neutral replacement + quotes beautification\n      breaks: enableBreaks    // Convert '\\n' in paragraphs into <br>\n    })\n\n    // Render markdown to HTML\n    return md.render(content)\n  }, [content, enableHtml, enableBreaks, enableTypographer])\n\n  // Initialize highlight.js after content is rendered\n  useEffect(() => {\n    hljs.highlightAll()\n  }, [processedContent])\n\n  return (\n    <div\n      className={`prose prose-base sm:prose-lg max-w-none dark:prose-invert markdown-renderer enhanced-markdown-content ${className}`}\n      data-color-mode=\"auto\"\n      dangerouslySetInnerHTML={{ __html: processedContent }}\n      style={{\n        backgroundColor: 'transparent',\n        color: 'inherit'\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;;;AAgBe,SAAS,gBAAgB,EACtC,OAAO,EACP,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,eAAe,KAAK,EACpB,oBAAoB,IAAI,EACH;;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE;YAC/B,sCAAsC;YACtC,MAAM,KAAK,IAAI,kJAAA,CAAA,UAAU,CAAC;gBACxB,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,QAAQ,aAAgB,uCAAuC;YACjE;YAEA,0BAA0B;YAC1B,OAAO,GAAG,MAAM,CAAC;QACnB;oDAAG;QAAC;QAAS;QAAY;QAAc;KAAkB;IAEzD,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,iKAAA,CAAA,UAAI,CAAC,YAAY;QACnB;oCAAG;QAAC;KAAiB;IAErB,qBACE,6LAAC;QACC,WAAW,CAAC,sGAAsG,EAAE,WAAW;QAC/H,mBAAgB;QAChB,yBAAyB;YAAE,QAAQ;QAAiB;QACpD,OAAO;YACL,iBAAiB;YACjB,OAAO;QACT;;;;;;AAGN;GApCwB;KAAA", "debugId": null}}, {"offset": {"line": 1015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test2/my-supabase-blog/src/app/test-editor/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { CherryEditor } from '@/components/cherry-editor'\nimport { EnhancedMarkdownEditor } from '@/components/enhanced-markdown-editor'\nimport MarkdownContent from '@/components/markdown-content'\n\nconst sampleMarkdown = `# Enhanced Markdown Editor Test\n\nThis is a test of the improved markdown editor with all the new features.\n\n## Features Tested\n\n### 1. Basic Formatting\n- **Bold text**\n- *Italic text*\n- ~~Strikethrough text~~\n- \\`Inline code\\`\n\n### 2. Code Blocks\n\\`\\`\\`javascript\nfunction greet(name) {\n  console.log(\\`Hello, \\${name}!\\`);\n  return \\`Welcome to our blog, \\${name}\\`;\n}\n\ngreet('World');\n\\`\\`\\`\n\n\\`\\`\\`python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibon<PERSON>ci(n-2)\n\nprint(fibonacci(10))\n\\`\\`\\`\n\n### 3. Lists\n1. First ordered item\n2. Second ordered item\n   - Nested unordered item\n   - Another nested item\n3. Third ordered item\n\n### 4. Links and Images\n[Visit our homepage](/)\n![Sample Image](https://via.placeholder.com/400x200)\n\n### 5. Tables\n| Feature | Status | Notes |\n|---------|--------|-------|\n| Word Count | ✅ | Shows real-time count |\n| Auto-save | ✅ | Saves every 3 seconds |\n| Syntax Highlighting | ✅ | Multiple languages |\n| Dark Mode | ✅ | Follows system theme |\n\n### 6. Blockquotes\n> This is a blockquote with some important information.\n> It can span multiple lines and looks great!\n\n### 7. Horizontal Rule\n---\n\n## Testing Notes\n- Test responsiveness on mobile devices\n- Verify dark/light mode switching\n- Check keyboard shortcuts (Ctrl+S, Ctrl+B, etc.)\n- Validate accessibility features\n- Test auto-save functionality\n`\n\nexport default function TestEditorPage() {\n  const [basicContent, setBasicContent] = useState(sampleMarkdown)\n  const [enhancedContent, setEnhancedContent] = useState(sampleMarkdown)\n  const [previewContent, setPreviewContent] = useState(sampleMarkdown)\n\n  const handleAutoSave = async (content: string) => {\n    console.log('Auto-saving content:', content.substring(0, 50) + '...')\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 500))\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-foreground mb-2\">\n            Markdown Editor Test Page\n          </h1>\n          <p className=\"text-muted-foreground\">\n            Test all the enhanced features of the improved markdown editor.\n          </p>\n        </div>\n\n        <div className=\"grid gap-8\">\n          {/* Basic Editor Test */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold text-foreground\">\n              1. Basic Enhanced Editor\n            </h2>\n            <CherryEditor\n              value={basicContent}\n              onChange={setBasicContent}\n              minHeight=\"400px\"\n              placeholder=\"Test the basic enhanced editor...\"\n              showWordCount={true}\n              enablePreview={true}\n              enableFullscreen={true}\n            />\n          </section>\n\n          {/* Enhanced Editor with Auto-save */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold text-foreground\">\n              2. Enhanced Editor with Auto-save\n            </h2>\n            <EnhancedMarkdownEditor\n              value={enhancedContent}\n              onChange={setEnhancedContent}\n              onAutoSave={handleAutoSave}\n              minHeight=\"400px\"\n              placeholder=\"Test the enhanced editor with auto-save...\"\n              enableAutoSave={true}\n              autoSaveDelay={3000}\n              showWordCount={true}\n              enablePreview={true}\n              enableFullscreen={true}\n              label=\"Enhanced Editor\"\n              required={true}\n            />\n          </section>\n\n          {/* Markdown Preview Test */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold text-foreground\">\n              3. Markdown Preview with Syntax Highlighting\n            </h2>\n            <div className=\"grid md:grid-cols-2 gap-4\">\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">Editor</h3>\n                <CherryEditor\n                  value={previewContent}\n                  onChange={setPreviewContent}\n                  minHeight=\"300px\"\n                  enablePreview={false}\n                  showWordCount={false}\n                />\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">Preview</h3>\n                <div className=\"border border-border rounded-lg p-4 bg-card min-h-[300px]\">\n                  <MarkdownContent \n                    content={previewContent}\n                    enableHtml={true}\n                    enableTypographer={true}\n                  />\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* Feature Checklist */}\n          <section className=\"space-y-4\">\n            <h2 className=\"text-2xl font-semibold text-foreground\">\n              4. Feature Checklist\n            </h2>\n            <div className=\"bg-card border border-border rounded-lg p-6\">\n              <h3 className=\"text-lg font-medium mb-4\">Test the following features:</h3>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium\">Editor Features:</h4>\n                  <ul className=\"space-y-1 text-sm text-muted-foreground\">\n                    <li>✅ Word count display</li>\n                    <li>✅ Auto-save functionality</li>\n                    <li>✅ Fullscreen mode</li>\n                    <li>✅ Live preview</li>\n                    <li>✅ Enhanced toolbar</li>\n                    <li>✅ Keyboard shortcuts</li>\n                  </ul>\n                </div>\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium\">Rendering Features:</h4>\n                  <ul className=\"space-y-1 text-sm text-muted-foreground\">\n                    <li>✅ Syntax highlighting</li>\n                    <li>✅ Dark/light mode</li>\n                    <li>✅ Responsive design</li>\n                    <li>✅ Typography improvements</li>\n                    <li>✅ Table styling</li>\n                    <li>✅ Code block styling</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </section>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DxB,CAAC;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,iBAAiB,OAAO;QAC5B,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,MAAM;QAC/D,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACnD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC,yIAAA,CAAA,eAAY;oCACX,OAAO;oCACP,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,eAAe;oCACf,eAAe;oCACf,kBAAkB;;;;;;;;;;;;sCAKtB,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC,uJAAA,CAAA,yBAAsB;oCACrB,OAAO;oCACP,UAAU;oCACV,YAAY;oCACZ,WAAU;oCACV,aAAY;oCACZ,gBAAgB;oCAChB,eAAe;oCACf,eAAe;oCACf,eAAe;oCACf,kBAAkB;oCAClB,OAAM;oCACN,UAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,6LAAC,yIAAA,CAAA,eAAY;oDACX,OAAO;oDACP,UAAU;oDACV,WAAU;oDACV,eAAe;oDACf,eAAe;;;;;;;;;;;;sDAGnB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2B;;;;;;8DACzC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4IAAA,CAAA,UAAe;wDACd,SAAS;wDACT,YAAY;wDACZ,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7B,6LAAC;4BAAQ,WAAU;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAc;;;;;;sEAC5B,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;;;;;;;;;;;;;8DAGR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAc;;;;;;sEAC5B,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;GA/HwB;KAAA", "debugId": null}}]}