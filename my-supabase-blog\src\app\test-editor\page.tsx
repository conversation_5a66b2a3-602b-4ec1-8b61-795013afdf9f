'use client'

import { useState } from 'react'
import { CherryEditor } from '@/components/cherry-editor'
import { EnhancedMarkdownEditor } from '@/components/enhanced-markdown-editor'
import MarkdownContent from '@/components/markdown-content'

const sampleMarkdown = `# Enhanced Markdown Editor Test

This is a test of the improved markdown editor with all the new features.

## Features Tested

### 1. Basic Formatting
- **Bold text**
- *Italic text*
- ~~Strikethrough text~~
- \`Inline code\`

### 2. Code Blocks
\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to our blog, \${name}\`;
}

greet('World');
\`\`\`

\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibon<PERSON>ci(n-2)

print(fibonacci(10))
\`\`\`

### 3. Lists
1. First ordered item
2. Second ordered item
   - Nested unordered item
   - Another nested item
3. Third ordered item

### 4. Links and Images
[Visit our homepage](/)
![Sample Image](https://via.placeholder.com/400x200)

### 5. Tables
| Feature | Status | Notes |
|---------|--------|-------|
| Word Count | ✅ | Shows real-time count |
| Auto-save | ✅ | Saves every 3 seconds |
| Syntax Highlighting | ✅ | Multiple languages |
| Dark Mode | ✅ | Follows system theme |

### 6. Blockquotes
> This is a blockquote with some important information.
> It can span multiple lines and looks great!

### 7. Horizontal Rule
---

## Testing Notes
- Test responsiveness on mobile devices
- Verify dark/light mode switching
- Check keyboard shortcuts (Ctrl+S, Ctrl+B, etc.)
- Validate accessibility features
- Test auto-save functionality
`

export default function TestEditorPage() {
  const [basicContent, setBasicContent] = useState(sampleMarkdown)
  const [enhancedContent, setEnhancedContent] = useState(sampleMarkdown)
  const [previewContent, setPreviewContent] = useState(sampleMarkdown)

  const handleAutoSave = async (content: string) => {
    console.log('Auto-saving content:', content.substring(0, 50) + '...')
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Markdown Editor Test Page
          </h1>
          <p className="text-muted-foreground">
            Test all the enhanced features of the improved markdown editor.
          </p>
        </div>

        <div className="grid gap-8">
          {/* Basic Editor Test */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold text-foreground">
              1. Basic Enhanced Editor
            </h2>
            <CherryEditor
              value={basicContent}
              onChange={setBasicContent}
              minHeight="400px"
              placeholder="Test the basic enhanced editor..."
              showWordCount={true}
              enablePreview={true}
              enableFullscreen={true}
            />
          </section>

          {/* Enhanced Editor with Auto-save */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold text-foreground">
              2. Enhanced Editor with Auto-save
            </h2>
            <EnhancedMarkdownEditor
              value={enhancedContent}
              onChange={setEnhancedContent}
              onAutoSave={handleAutoSave}
              minHeight="400px"
              placeholder="Test the enhanced editor with auto-save..."
              enableAutoSave={true}
              autoSaveDelay={3000}
              showWordCount={true}
              enablePreview={true}
              enableFullscreen={true}
              label="Enhanced Editor"
              required={true}
            />
          </section>

          {/* Markdown Preview Test */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold text-foreground">
              3. Markdown Preview with Syntax Highlighting
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Editor</h3>
                <CherryEditor
                  value={previewContent}
                  onChange={setPreviewContent}
                  minHeight="300px"
                  enablePreview={false}
                  showWordCount={false}
                />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Preview</h3>
                <div className="border border-border rounded-lg p-4 bg-card min-h-[300px]">
                  <MarkdownContent 
                    content={previewContent}
                    enableHtml={true}
                    enableTypographer={true}
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Feature Checklist */}
          <section className="space-y-4">
            <h2 className="text-2xl font-semibold text-foreground">
              4. Feature Checklist
            </h2>
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Test the following features:</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Editor Features:</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>✅ Word count display</li>
                    <li>✅ Auto-save functionality</li>
                    <li>✅ Fullscreen mode</li>
                    <li>✅ Live preview</li>
                    <li>✅ Enhanced toolbar</li>
                    <li>✅ Keyboard shortcuts</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Rendering Features:</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>✅ Syntax highlighting</li>
                    <li>✅ Dark/light mode</li>
                    <li>✅ Responsive design</li>
                    <li>✅ Typography improvements</li>
                    <li>✅ Table styling</li>
                    <li>✅ Code block styling</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
}
