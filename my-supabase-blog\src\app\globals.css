@import "tailwindcss";

/* Markdown Editor Styles */
@import "@uiw/react-md-editor/markdown-editor.css";
@import "@uiw/react-markdown-preview/markdown.css";

/* Fix for MDEditor display issues */
.w-md-editor {
  background-color: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  min-height: 400px !important;
  color: hsl(var(--foreground)) !important;
  /* Override MDEditor CSS variables */
  --color-canvas-default: hsl(var(--background));
  --color-fg-default: hsl(var(--foreground));
  --color-border-default: hsl(var(--border));
  --color-fg-muted: hsl(var(--muted-foreground));
}

.w-md-editor-text-textarea,
.w-md-editor-text {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  min-height: 300px !important;
}

.w-md-editor-text-input {
  background-color: transparent !important;
  color: hsl(var(--foreground)) !important;
  caret-color: hsl(var(--foreground)) !important;
  /* Fix for invisible text input */
  -webkit-text-fill-color: hsl(var(--foreground)) !important;
}

.w-md-editor-text-pre {
  background-color: transparent !important;
  color: transparent !important;
}

.w-md-editor-preview {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  min-height: 300px !important;
}

.w-md-editor-toolbar {
  background-color: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
  padding: 8px 12px !important;
}

.w-md-editor-toolbar button {
  color: hsl(var(--foreground)) !important;
  background-color: transparent !important;
  border-radius: 6px !important;
  padding: 6px 8px !important;
  margin: 0 2px !important;
  transition: all 0.2s ease-in-out !important;
}

.w-md-editor-toolbar button:hover {
  background-color: hsl(var(--muted)) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.w-md-editor-toolbar button:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.w-md-editor-toolbar .w-md-editor-toolbar-divider {
  background-color: hsl(var(--border)) !important;
  margin: 0 8px !important;
}

/* Ensure the editor is visible */
.w-md-editor * {
  box-sizing: border-box;
}

.w-md-editor-text-container {
  display: flex !important;
  flex: 1 !important;
}

.w-md-editor-text-pre,
.w-md-editor-text-input {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix for content area */
.w-md-editor-content {
  background-color: hsl(var(--background)) !important;
}

/* Fix for markdown preview content */
.w-md-editor .wmde-markdown {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* Force visibility for all editor elements */
.w-md-editor-text-area,
.w-md-editor-input,
.w-md-editor-focus {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* Enhanced markdown editor wrapper styles */
.enhanced-md-editor-wrapper {
  position: relative;
  z-index: 1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.enhanced-md-editor-wrapper:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.enhanced-md-editor-wrapper .w-md-editor {
  border-radius: 12px 12px 0 0;
  border: 1px solid hsl(var(--border));
  overflow: hidden;
}

/* Legacy wrapper styles for backward compatibility */
.md-editor-wrapper {
  position: relative;
  z-index: 1;
}

.md-editor-wrapper .w-md-editor {
  border-radius: 8px;
  overflow: hidden;
}

/* Ensure text is visible in all states */
.w-md-editor-text-input:focus,
.w-md-editor-text-input:active,
.w-md-editor-text-input {
  color: hsl(var(--foreground)) !important;
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

/* Fix for potential z-index issues */
.w-md-editor-text-container > * {
  position: relative;
  z-index: 1;
}

/* Ensure toolbar icons are visible */
.w-md-editor-toolbar svg {
  fill: hsl(var(--foreground)) !important;
  color: hsl(var(--foreground)) !important;
}

/* Force text input visibility */
.w-md-editor-text-input {
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  -webkit-text-fill-color: hsl(var(--foreground)) !important;
  text-shadow: none !important;
}

/* Utility Classes */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Animation utilities */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
  opacity: 0;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--primary), 0);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Touch improvements */
@media (hover: none) and (pointer: coarse) {
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Increase touch targets on mobile */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Focus improvements for accessibility */
*:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #171717;
  --primary-foreground: #fafafa;
  --secondary: #f5f5f5;
  --secondary-foreground: #171717;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #f5f5f5;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --destructive-foreground: #fafafa;
  --border: #e5e5e5;
  --input: #e5e5e5;
  --ring: #171717;
  --radius: 0.5rem;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --card: #0a0a0a;
  --card-foreground: #ededed;
  --popover: #0a0a0a;
  --popover-foreground: #ededed;
  --primary: #ededed;
  --primary-foreground: #171717;
  --secondary: #262626;
  --secondary-foreground: #ededed;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #262626;
  --accent-foreground: #ededed;
  --destructive: #dc2626;
  --destructive-foreground: #ededed;
  --border: #262626;
  --input: #262626;
  --ring: #d4d4d8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Cherry Markdown 編輯器樣式 */
.cherry-editor-wrapper {
  @apply w-full;
}

.cherry-editor .cherry {
  @apply border-0 bg-background text-foreground;
}

.cherry-editor .cherry-toolbar {
  @apply bg-card border-b border-border;
}

.cherry-editor .cherry-toolbar .toolbar-item {
  @apply text-foreground hover:bg-muted;
}

.cherry-editor .cherry-editor {
  @apply bg-background text-foreground;
}

.cherry-editor .cherry-previewer {
  @apply bg-background text-foreground;
}

.cherry-editor .cherry-previewer .cherry-markdown {
  @apply text-foreground;
}

/* Cherry Markdown 深色模式 */
.dark .cherry-editor .cherry {
  @apply bg-background text-foreground;
}

.dark .cherry-editor .cherry-toolbar {
  @apply bg-card border-border;
}

.dark .cherry-editor .cherry-toolbar .toolbar-item {
  @apply text-foreground;
}

.dark .cherry-editor .cherry-editor {
  @apply bg-background text-foreground;
}

.dark .cherry-editor .cherry-previewer {
  @apply bg-background text-foreground;
}

/* Enhanced Markdown Content Styles */
.enhanced-markdown-content {
  line-height: 1.7;
  font-size: 16px;
}

.enhanced-markdown-content h1,
.enhanced-markdown-content h2,
.enhanced-markdown-content h3,
.enhanced-markdown-content h4,
.enhanced-markdown-content h5,
.enhanced-markdown-content h6 {
  color: hsl(var(--foreground));
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.enhanced-markdown-content h1 {
  font-size: 2.25rem;
  border-bottom: 2px solid hsl(var(--border));
  padding-bottom: 0.5rem;
}

.enhanced-markdown-content h2 {
  font-size: 1.875rem;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.25rem;
}

.enhanced-markdown-content h3 {
  font-size: 1.5rem;
}

.enhanced-markdown-content h4 {
  font-size: 1.25rem;
}

.enhanced-markdown-content p {
  margin-bottom: 1rem;
  color: hsl(var(--muted-foreground));
}

.enhanced-markdown-content a {
  color: hsl(var(--primary));
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease-in-out;
}

.enhanced-markdown-content a:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration: underline;
}

.enhanced-markdown-content code {
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.enhanced-markdown-content pre {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.enhanced-markdown-content pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
}

.enhanced-markdown-content blockquote {
  border-left: 4px solid hsl(var(--primary));
  background-color: hsl(var(--muted) / 0.3);
  padding: 1rem 1.5rem;
  margin: 1rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

.enhanced-markdown-content ul,
.enhanced-markdown-content ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.enhanced-markdown-content li {
  margin-bottom: 0.5rem;
  color: hsl(var(--muted-foreground));
}

.enhanced-markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.enhanced-markdown-content th,
.enhanced-markdown-content td {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
  text-align: left;
}

.enhanced-markdown-content th {
  background-color: hsl(var(--muted));
  font-weight: 600;
  color: hsl(var(--foreground));
}

.enhanced-markdown-content td {
  color: hsl(var(--muted-foreground));
}

.enhanced-markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.enhanced-markdown-content hr {
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, hsl(var(--border)), transparent);
  margin: 2rem 0;
}

/* Syntax highlighting styles */
.enhanced-markdown-content .hljs {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

.enhanced-markdown-content .hljs-keyword,
.enhanced-markdown-content .hljs-selector-tag,
.enhanced-markdown-content .hljs-literal,
.enhanced-markdown-content .hljs-section,
.enhanced-markdown-content .hljs-link {
  color: hsl(var(--primary)) !important;
}

.enhanced-markdown-content .hljs-string,
.enhanced-markdown-content .hljs-title,
.enhanced-markdown-content .hljs-name,
.enhanced-markdown-content .hljs-type,
.enhanced-markdown-content .hljs-attribute,
.enhanced-markdown-content .hljs-symbol,
.enhanced-markdown-content .hljs-bullet,
.enhanced-markdown-content .hljs-addition,
.enhanced-markdown-content .hljs-variable,
.enhanced-markdown-content .hljs-template-tag,
.enhanced-markdown-content .hljs-template-variable {
  color: #22c55e !important;
}

.enhanced-markdown-content .hljs-comment,
.enhanced-markdown-content .hljs-quote,
.enhanced-markdown-content .hljs-deletion,
.enhanced-markdown-content .hljs-meta {
  color: hsl(var(--muted-foreground)) !important;
  font-style: italic;
}

.enhanced-markdown-content .hljs-number,
.enhanced-markdown-content .hljs-regexp,
.enhanced-markdown-content .hljs-literal {
  color: #f59e0b !important;
}

.enhanced-markdown-content .hljs-built_in,
.enhanced-markdown-content .hljs-builtin-name,
.enhanced-markdown-content .hljs-class .hljs-title {
  color: #3b82f6 !important;
}

/* Legacy Markdown renderer styles for backward compatibility */
.markdown-renderer .prose {
  @apply text-foreground;
}

.markdown-renderer .prose h1,
.markdown-renderer .prose h2,
.markdown-renderer .prose h3,
.markdown-renderer .prose h4,
.markdown-renderer .prose h5,
.markdown-renderer .prose h6 {
  @apply text-foreground;
}

.markdown-renderer .prose a {
  @apply text-primary hover:text-primary/80;
}

.markdown-renderer .prose code {
  @apply bg-muted text-foreground px-1 py-0.5 rounded;
}

.markdown-renderer .prose pre {
  @apply bg-muted border border-border;
}

.markdown-renderer .prose blockquote {
  @apply border-l-primary text-muted-foreground;
}

.markdown-renderer .prose table {
  @apply border-border;
}

.markdown-renderer .prose th,
.markdown-renderer .prose td {
  @apply border-border;
}

.markdown-renderer .prose th {
  @apply bg-muted;
}

/* 動畫效果 - Updated to use consistent animation names and properties */
@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slide-in 0.5s ease-out forwards;
  opacity: 0;
}

/* 懸停效果 */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 按鈕動畫 */
.btn-primary {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* 載入動畫 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 脈衝動畫 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .text-responsive {
    font-size: 0.875rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .hide-mobile {
    display: none;
  }

  .text-responsive {
    font-size: 0.75rem;
  }
}

/* Utility classes */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced animations */
@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary), 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary), 0.8);
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s ease-out forwards;
  opacity: 0;
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out forwards;
  opacity: 0;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Improved hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}
