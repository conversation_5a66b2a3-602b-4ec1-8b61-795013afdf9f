pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Twilight
  Author: <PERSON> (https://github.com/hartbit)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme twilight
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1e1e1e  Default Background
base01  #323537  Lighter Background (Used for status bars, line number and folding marks)
base02  #464b50  Selection Background
base03  #5f5a60  Comments, Invisibles, Line Highlighting
base04  #838184  Dark Foreground (Used for status bars)
base05  #a7a7a7  Default Foreground, Caret, Delimiters, Operators
base06  #c3c3c3  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #cf6a4c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #cda869  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f9ee98  Classes, Markup Bold, Search Text Background
base0B  #8f9d6a  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #afc4db  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #7587a6  Functions, Methods, Attribute IDs, Headings
base0E  #9b859d  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #9b703f  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a7a7a7;
  background: #1e1e1e
}
.hljs::selection,
.hljs ::selection {
  background-color: #464b50;
  color: #a7a7a7
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5f5a60 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5f5a60
}
/* base04 - #838184 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #838184
}
/* base05 - #a7a7a7 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a7a7a7
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #cf6a4c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #cda869
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f9ee98
}
.hljs-strong {
  font-weight: bold;
  color: #f9ee98
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #8f9d6a
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #afc4db
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #7587a6
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9b859d
}
.hljs-emphasis {
  color: #9b859d;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #9b703f
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}