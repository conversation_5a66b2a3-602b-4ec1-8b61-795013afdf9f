pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Gruvbox light, hard
  Author: <PERSON><PERSON><PERSON> (<EMAIL>), morhetz (https://github.com/morhetz/gruvbox)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme gruvbox-light-hard
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f9f5d7  Default Background
base01  #ebdbb2  Lighter Background (Used for status bars, line number and folding marks)
base02  #d5c4a1  Selection Background
base03  #bdae93  Comments, Invisibles, Line Highlighting
base04  #665c54  Dark Foreground (Used for status bars)
base05  #504945  Default Foreground, <PERSON>t, Delimiters, Operators
base06  #3c3836  Light Foreground (Not often used)
base07  #282828  Light Background (Not often used)
base08  #9d0006  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #af3a03  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #b57614  Classes, Markup Bold, Search Text Background
base0B  #79740e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #427b58  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #076678  Functions, Methods, Attribute IDs, Headings
base0E  #8f3f71  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #d65d0e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #504945;
  background: #f9f5d7
}
.hljs::selection,
.hljs ::selection {
  background-color: #d5c4a1;
  color: #504945
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #bdae93 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #bdae93
}
/* base04 - #665c54 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #665c54
}
/* base05 - #504945 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #504945
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #9d0006
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #af3a03
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #b57614
}
.hljs-strong {
  font-weight: bold;
  color: #b57614
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #79740e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #427b58
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #076678
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #8f3f71
}
.hljs-emphasis {
  color: #8f3f71;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #d65d0e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}