{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@uiw/react-markdown-preview/markdown.css"], "sourcesContent": ["@media (prefers-color-scheme: dark) {\n  .wmde-markdown,\n  .wmde-markdown-var {\n    color-scheme: dark;\n    --color-prettylights-syntax-comment: #8b949e;\n    --color-prettylights-syntax-constant: #79c0ff;\n    --color-prettylights-syntax-entity: #d2a8ff;\n    --color-prettylights-syntax-storage-modifier-import: #c9d1d9;\n    --color-prettylights-syntax-entity-tag: #7ee787;\n    --color-prettylights-syntax-keyword: #ff7b72;\n    --color-prettylights-syntax-string: #a5d6ff;\n    --color-prettylights-syntax-variable: #ffa657;\n    --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;\n    --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;\n    --color-prettylights-syntax-invalid-illegal-bg: #8e1519;\n    --color-prettylights-syntax-carriage-return-text: #f0f6fc;\n    --color-prettylights-syntax-carriage-return-bg: #b62324;\n    --color-prettylights-syntax-string-regexp: #7ee787;\n    --color-prettylights-syntax-markup-list: #f2cc60;\n    --color-prettylights-syntax-markup-heading: #1f6feb;\n    --color-prettylights-syntax-markup-italic: #c9d1d9;\n    --color-prettylights-syntax-markup-bold: #c9d1d9;\n    --color-prettylights-syntax-markup-deleted-text: #ffdcd7;\n    --color-prettylights-syntax-markup-deleted-bg: #67060c;\n    --color-prettylights-syntax-markup-inserted-text: #aff5b4;\n    --color-prettylights-syntax-markup-inserted-bg: #033a16;\n    --color-prettylights-syntax-markup-changed-text: #ffdfb6;\n    --color-prettylights-syntax-markup-changed-bg: #5a1e02;\n    --color-prettylights-syntax-markup-ignored-text: #c9d1d9;\n    --color-prettylights-syntax-markup-ignored-bg: #1158c7;\n    --color-prettylights-syntax-meta-diff-range: #d2a8ff;\n    --color-prettylights-syntax-brackethighlighter-angle: #8b949e;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;\n    --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;\n    --color-fg-default: #c9d1d9;\n    --color-fg-muted: #8b949e;\n    --color-fg-subtle: #484f58;\n    --color-canvas-default: #0d1117;\n    --color-canvas-subtle: #161b22;\n    --color-border-default: #30363d;\n    --color-border-muted: #21262d;\n    --color-neutral-muted: rgba(110, 118, 129, 0.4);\n    --color-accent-fg: #58a6ff;\n    --color-accent-emphasis: #1f6feb;\n    --color-attention-subtle: rgba(187, 128, 9, 0.15);\n    --color-danger-fg: #f85149;\n    --color-danger-emphasis: #da3633;\n    --color-attention-fg: #d29922;\n    --color-attention-emphasis: #9e6a03;\n    --color-done-fg: #a371f7;\n    --color-done-emphasis: #8957e5;\n    --color-success-fg: #3fb950;\n    --color-success-emphasis: #238636;\n    --color-copied-active-bg: #2e9b33;\n  }\n}\n@media (prefers-color-scheme: light) {\n  .wmde-markdown,\n  .wmde-markdown-var {\n    color-scheme: light;\n    --color-prettylights-syntax-comment: #6e7781;\n    --color-prettylights-syntax-constant: #0550ae;\n    --color-prettylights-syntax-entity: #8250df;\n    --color-prettylights-syntax-storage-modifier-import: #24292f;\n    --color-prettylights-syntax-entity-tag: #116329;\n    --color-prettylights-syntax-keyword: #cf222e;\n    --color-prettylights-syntax-string: #0a3069;\n    --color-prettylights-syntax-variable: #953800;\n    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;\n    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;\n    --color-prettylights-syntax-invalid-illegal-bg: #82071e;\n    --color-prettylights-syntax-carriage-return-text: #f6f8fa;\n    --color-prettylights-syntax-carriage-return-bg: #cf222e;\n    --color-prettylights-syntax-string-regexp: #116329;\n    --color-prettylights-syntax-markup-list: #3b2300;\n    --color-prettylights-syntax-markup-heading: #0550ae;\n    --color-prettylights-syntax-markup-italic: #24292f;\n    --color-prettylights-syntax-markup-bold: #24292f;\n    --color-prettylights-syntax-markup-deleted-text: #82071e;\n    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;\n    --color-prettylights-syntax-markup-inserted-text: #116329;\n    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;\n    --color-prettylights-syntax-markup-changed-text: #953800;\n    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;\n    --color-prettylights-syntax-markup-ignored-text: #eaeef2;\n    --color-prettylights-syntax-markup-ignored-bg: #0550ae;\n    --color-prettylights-syntax-meta-diff-range: #8250df;\n    --color-prettylights-syntax-brackethighlighter-angle: #57606a;\n    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;\n    --color-prettylights-syntax-constant-other-reference-link: #0a3069;\n    --color-fg-default: #24292f;\n    --color-fg-muted: #57606a;\n    --color-fg-subtle: #6e7781;\n    --color-canvas-default: #ffffff;\n    --color-canvas-subtle: #f6f8fa;\n    --color-border-default: #d0d7de;\n    --color-border-muted: hsl(210, 18%, 87%);\n    --color-neutral-muted: rgba(175, 184, 193, 0.2);\n    --color-accent-fg: #0969da;\n    --color-accent-emphasis: #0969da;\n    --color-attention-subtle: #fff8c5;\n    --color-danger-fg: #d1242f;\n    --color-danger-emphasis: #cf222e;\n    --color-attention-fg: #9a6700;\n    --color-attention-emphasis: #9a6700;\n    --color-done-fg: #8250df;\n    --color-done-emphasis: #8250df;\n    --color-success-fg: #1a7f37;\n    --color-success-emphasis: #1f883d;\n    --color-copied-active-bg: #2e9b33;\n  }\n}\n[data-color-mode*='dark'] .wmde-markdown,\n[data-color-mode*='dark'] .wmde-markdown-var,\n.wmde-markdown-var[data-color-mode*='dark'],\n.wmde-markdown[data-color-mode*='dark'],\nbody[data-color-mode*='dark'] {\n  color-scheme: dark;\n  --color-prettylights-syntax-comment: #8b949e;\n  --color-prettylights-syntax-constant: #79c0ff;\n  --color-prettylights-syntax-entity: #d2a8ff;\n  --color-prettylights-syntax-storage-modifier-import: #c9d1d9;\n  --color-prettylights-syntax-entity-tag: #7ee787;\n  --color-prettylights-syntax-keyword: #ff7b72;\n  --color-prettylights-syntax-string: #a5d6ff;\n  --color-prettylights-syntax-variable: #ffa657;\n  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;\n  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;\n  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;\n  --color-prettylights-syntax-carriage-return-text: #f0f6fc;\n  --color-prettylights-syntax-carriage-return-bg: #b62324;\n  --color-prettylights-syntax-string-regexp: #7ee787;\n  --color-prettylights-syntax-markup-list: #f2cc60;\n  --color-prettylights-syntax-markup-heading: #1f6feb;\n  --color-prettylights-syntax-markup-italic: #c9d1d9;\n  --color-prettylights-syntax-markup-bold: #c9d1d9;\n  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;\n  --color-prettylights-syntax-markup-deleted-bg: #67060c;\n  --color-prettylights-syntax-markup-inserted-text: #aff5b4;\n  --color-prettylights-syntax-markup-inserted-bg: #033a16;\n  --color-prettylights-syntax-markup-changed-text: #ffdfb6;\n  --color-prettylights-syntax-markup-changed-bg: #5a1e02;\n  --color-prettylights-syntax-markup-ignored-text: #c9d1d9;\n  --color-prettylights-syntax-markup-ignored-bg: #1158c7;\n  --color-prettylights-syntax-meta-diff-range: #d2a8ff;\n  --color-prettylights-syntax-brackethighlighter-angle: #8b949e;\n  --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;\n  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;\n  --color-fg-default: #c9d1d9;\n  --color-fg-muted: #8b949e;\n  --color-fg-subtle: #484f58;\n  --color-canvas-default: #0d1117;\n  --color-canvas-subtle: #161b22;\n  --color-border-default: #30363d;\n  --color-border-muted: #21262d;\n  --color-neutral-muted: rgba(110, 118, 129, 0.4);\n  --color-accent-fg: #58a6ff;\n  --color-accent-emphasis: #1f6feb;\n  --color-attention-subtle: rgba(187, 128, 9, 0.15);\n  --color-danger-fg: #f85149;\n}\n[data-color-mode*='light'] .wmde-markdown,\n[data-color-mode*='light'] .wmde-markdown-var,\n.wmde-markdown-var[data-color-mode*='light'],\n.wmde-markdown[data-color-mode*='light'],\nbody[data-color-mode*='light'] {\n  color-scheme: light;\n  --color-prettylights-syntax-comment: #6e7781;\n  --color-prettylights-syntax-constant: #0550ae;\n  --color-prettylights-syntax-entity: #8250df;\n  --color-prettylights-syntax-storage-modifier-import: #24292f;\n  --color-prettylights-syntax-entity-tag: #116329;\n  --color-prettylights-syntax-keyword: #cf222e;\n  --color-prettylights-syntax-string: #0a3069;\n  --color-prettylights-syntax-variable: #953800;\n  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;\n  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;\n  --color-prettylights-syntax-invalid-illegal-bg: #82071e;\n  --color-prettylights-syntax-carriage-return-text: #f6f8fa;\n  --color-prettylights-syntax-carriage-return-bg: #cf222e;\n  --color-prettylights-syntax-string-regexp: #116329;\n  --color-prettylights-syntax-markup-list: #3b2300;\n  --color-prettylights-syntax-markup-heading: #0550ae;\n  --color-prettylights-syntax-markup-italic: #24292f;\n  --color-prettylights-syntax-markup-bold: #24292f;\n  --color-prettylights-syntax-markup-deleted-text: #82071e;\n  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;\n  --color-prettylights-syntax-markup-inserted-text: #116329;\n  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;\n  --color-prettylights-syntax-markup-changed-text: #953800;\n  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;\n  --color-prettylights-syntax-markup-ignored-text: #eaeef2;\n  --color-prettylights-syntax-markup-ignored-bg: #0550ae;\n  --color-prettylights-syntax-meta-diff-range: #8250df;\n  --color-prettylights-syntax-brackethighlighter-angle: #57606a;\n  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;\n  --color-prettylights-syntax-constant-other-reference-link: #0a3069;\n  --color-fg-default: #24292f;\n  --color-fg-muted: #57606a;\n  --color-fg-subtle: #6e7781;\n  --color-canvas-default: #ffffff;\n  --color-canvas-subtle: #f6f8fa;\n  --color-border-default: #d0d7de;\n  --color-border-muted: hsl(210, 18%, 87%);\n  --color-neutral-muted: rgba(175, 184, 193, 0.2);\n  --color-accent-fg: #0969da;\n  --color-accent-emphasis: #0969da;\n  --color-attention-subtle: #fff8c5;\n  --color-danger-fg: #cf222e;\n}\n.wmde-markdown {\n  -webkit-text-size-adjust: 100%;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n  color: var(--color-fg-default);\n  background-color: var(--color-canvas-default);\n}\n.wmde-markdown details,\n.wmde-markdown figcaption,\n.wmde-markdown figure {\n  display: block;\n}\n.wmde-markdown summary {\n  display: list-item;\n}\n.wmde-markdown [hidden] {\n  display: none !important;\n}\n.wmde-markdown a {\n  background-color: transparent;\n  color: var(--color-accent-fg);\n  text-decoration: none;\n}\n.wmde-markdown a:active,\n.wmde-markdown a:hover {\n  outline-width: 0;\n}\n.wmde-markdown abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n.wmde-markdown b,\n.wmde-markdown strong {\n  font-weight: 600;\n}\n.wmde-markdown dfn {\n  font-style: italic;\n}\n.wmde-markdown h1 {\n  margin: 0.67em 0;\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 2em;\n  border-bottom: 1px solid var(--color-border-muted);\n}\n.wmde-markdown mark {\n  background-color: var(--color-attention-subtle);\n  color: var(--color-text-primary);\n}\n.wmde-markdown small {\n  font-size: 90%;\n}\n.wmde-markdown sub,\n.wmde-markdown sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n.wmde-markdown sub {\n  bottom: -0.25em;\n}\n.wmde-markdown sup {\n  top: -0.5em;\n}\n.wmde-markdown img {\n  display: inline-block;\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n  background-color: var(--color-canvas-default);\n}\n.wmde-markdown code,\n.wmde-markdown kbd,\n.wmde-markdown pre,\n.wmde-markdown samp {\n  font-family: monospace, monospace;\n  font-size: 1em;\n}\n.wmde-markdown figure {\n  margin: 1em 40px;\n}\n.wmde-markdown hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border: 0;\n  border-bottom: 1px solid var(--color-border-muted);\n  height: 0.25em;\n  padding: 0;\n  margin: 24px 0;\n  background-color: var(--color-border-default);\n}\n.wmde-markdown input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n.wmde-markdown [type='button'],\n.wmde-markdown [type='reset'],\n.wmde-markdown [type='submit'] {\n  -webkit-appearance: button;\n}\n.wmde-markdown [type='button']::-moz-focus-inner,\n.wmde-markdown [type='reset']::-moz-focus-inner,\n.wmde-markdown [type='submit']::-moz-focus-inner {\n  border-style: none;\n  padding: 0;\n}\n.wmde-markdown [type='button']:-moz-focusring,\n.wmde-markdown [type='reset']:-moz-focusring,\n.wmde-markdown [type='submit']:-moz-focusring {\n  outline: 1px dotted ButtonText;\n}\n.wmde-markdown [type='checkbox'],\n.wmde-markdown [type='radio'] {\n  box-sizing: border-box;\n  padding: 0;\n}\n.wmde-markdown [type='number']::-webkit-inner-spin-button,\n.wmde-markdown [type='number']::-webkit-outer-spin-button {\n  height: auto;\n}\n.wmde-markdown [type='search'] {\n  -webkit-appearance: textfield;\n  outline-offset: -2px;\n}\n.wmde-markdown [type='search']::-webkit-search-cancel-button,\n.wmde-markdown [type='search']::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n.wmde-markdown ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: 0.54;\n}\n.wmde-markdown ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  font: inherit;\n}\n.wmde-markdown a:hover {\n  text-decoration: underline;\n}\n.wmde-markdown hr::before {\n  display: table;\n  content: '';\n}\n.wmde-markdown hr::after {\n  display: table;\n  clear: both;\n  content: '';\n}\n.wmde-markdown table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n}\n.wmde-markdown td,\n.wmde-markdown th {\n  padding: 0;\n}\n.wmde-markdown details summary {\n  cursor: pointer;\n}\n.wmde-markdown details:not([open]) > *:not(summary) {\n  display: none !important;\n}\n.wmde-markdown kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: var(--color-fg-default);\n  vertical-align: middle;\n  background-color: var(--color-canvas-subtle);\n  border: solid 1px var(--color-neutral-muted);\n  border-bottom-color: var(--color-neutral-muted);\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);\n}\n.wmde-markdown h1,\n.wmde-markdown h2,\n.wmde-markdown h3,\n.wmde-markdown h4,\n.wmde-markdown h5,\n.wmde-markdown h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.wmde-markdown td,\n.wmde-markdown th {\n  padding: 0;\n}\n.wmde-markdown details summary {\n  cursor: pointer;\n}\n.wmde-markdown details:not([open]) > *:not(summary) {\n  display: none !important;\n}\n.wmde-markdown kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: var(--color-fg-default);\n  vertical-align: middle;\n  background-color: var(--color-canvas-subtle);\n  border: solid 1px var(--color-neutral-muted);\n  border-bottom-color: var(--color-neutral-muted);\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 var(--color-neutral-muted);\n}\n.wmde-markdown h1,\n.wmde-markdown h2,\n.wmde-markdown h3,\n.wmde-markdown h4,\n.wmde-markdown h5,\n.wmde-markdown h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.wmde-markdown h2 {\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid var(--color-border-muted);\n}\n.wmde-markdown h3 {\n  font-weight: 600;\n  font-size: 1.25em;\n}\n.wmde-markdown h4 {\n  font-weight: 600;\n  font-size: 1em;\n}\n.wmde-markdown h5 {\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.wmde-markdown h6 {\n  font-weight: 600;\n  font-size: 0.85em;\n  color: var(--color-fg-muted);\n}\n.wmde-markdown p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n.wmde-markdown blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: var(--color-fg-muted);\n  border-left: 0.25em solid var(--color-border-default);\n}\n.wmde-markdown ul,\n.wmde-markdown ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n.wmde-markdown ol ol,\n.wmde-markdown ul ol {\n  list-style-type: lower-roman;\n}\n.wmde-markdown ul ul ol,\n.wmde-markdown ul ol ol,\n.wmde-markdown ol ul ol,\n.wmde-markdown ol ol ol {\n  list-style-type: lower-alpha;\n}\n.wmde-markdown dd {\n  margin-left: 0;\n}\n.wmde-markdown tt,\n.wmde-markdown code {\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n}\n.wmde-markdown pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n  word-wrap: normal;\n}\n.wmde-markdown .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n.wmde-markdown ::placeholder {\n  color: var(--color-fg-subtle);\n  opacity: 1;\n}\n.wmde-markdown input::-webkit-outer-spin-button,\n.wmde-markdown input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.wmde-markdown [data-catalyst] {\n  display: block;\n}\n.wmde-markdown::before {\n  display: table;\n  content: '';\n}\n.wmde-markdown::after {\n  display: table;\n  clear: both;\n  content: '';\n}\n.wmde-markdown > *:first-child {\n  margin-top: 0 !important;\n}\n.wmde-markdown > *:last-child {\n  margin-bottom: 0 !important;\n}\n.wmde-markdown a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n.wmde-markdown .absent {\n  color: var(--color-danger-fg);\n}\n.wmde-markdown a.anchor {\n  float: left;\n  padding-right: 4px;\n  margin-left: -20px;\n  line-height: 1;\n}\n.wmde-markdown .anchor:focus {\n  outline: none;\n}\n.wmde-markdown p,\n.wmde-markdown blockquote,\n.wmde-markdown ul,\n.wmde-markdown ol,\n.wmde-markdown dl,\n.wmde-markdown table,\n.wmde-markdown pre,\n.wmde-markdown details {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n.wmde-markdown blockquote > :first-child {\n  margin-top: 0;\n}\n.wmde-markdown blockquote > :last-child {\n  margin-bottom: 0;\n}\n.wmde-markdown sup > a::before {\n  content: '[';\n}\n.wmde-markdown sup > a::after {\n  content: ']';\n}\n.wmde-markdown h1 .octicon-link,\n.wmde-markdown h2 .octicon-link,\n.wmde-markdown h3 .octicon-link,\n.wmde-markdown h4 .octicon-link,\n.wmde-markdown h5 .octicon-link,\n.wmde-markdown h6 .octicon-link {\n  color: var(--color-fg-default);\n  vertical-align: middle;\n  visibility: hidden;\n}\n.wmde-markdown h1:hover .anchor,\n.wmde-markdown h2:hover .anchor,\n.wmde-markdown h3:hover .anchor,\n.wmde-markdown h4:hover .anchor,\n.wmde-markdown h5:hover .anchor,\n.wmde-markdown h6:hover .anchor {\n  text-decoration: none;\n}\n.wmde-markdown h1:hover .anchor .octicon-link,\n.wmde-markdown h2:hover .anchor .octicon-link,\n.wmde-markdown h3:hover .anchor .octicon-link,\n.wmde-markdown h4:hover .anchor .octicon-link,\n.wmde-markdown h5:hover .anchor .octicon-link,\n.wmde-markdown h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n.wmde-markdown h1 tt,\n.wmde-markdown h1 code,\n.wmde-markdown h2 tt,\n.wmde-markdown h2 code,\n.wmde-markdown h3 tt,\n.wmde-markdown h3 code,\n.wmde-markdown h4 tt,\n.wmde-markdown h4 code,\n.wmde-markdown h5 tt,\n.wmde-markdown h5 code,\n.wmde-markdown h6 tt,\n.wmde-markdown h6 code {\n  padding: 0 0.2em;\n  font-size: inherit;\n}\n.wmde-markdown ul.no-list,\n.wmde-markdown ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n.wmde-markdown ol[type='1'] {\n  list-style-type: decimal;\n}\n.wmde-markdown ol[type='a'] {\n  list-style-type: lower-alpha;\n}\n.wmde-markdown ol[type='i'] {\n  list-style-type: lower-roman;\n}\n.wmde-markdown div > ol:not([type]) {\n  list-style-type: decimal;\n}\n.wmde-markdown ul ul,\n.wmde-markdown ul ol,\n.wmde-markdown ol ol,\n.wmde-markdown ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.wmde-markdown li > p {\n  margin-top: 16px;\n}\n.wmde-markdown li + li {\n  margin-top: 0.25em;\n}\n.wmde-markdown dl {\n  padding: 0;\n}\n.wmde-markdown dl dt {\n  padding: 0;\n  margin-top: 16px;\n  font-size: 1em;\n  font-style: italic;\n  font-weight: 600;\n}\n.wmde-markdown dl dd {\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n.wmde-markdown table th {\n  font-weight: 600;\n}\n.wmde-markdown table th,\n.wmde-markdown table td {\n  padding: 6px 13px;\n  border: 1px solid var(--color-border-default);\n}\n.wmde-markdown table tr {\n  background-color: var(--color-canvas-default);\n  border-top: 1px solid var(--color-border-muted);\n}\n.wmde-markdown table tr:nth-child(2n) {\n  background-color: var(--color-canvas-subtle);\n}\n.wmde-markdown table img {\n  background-color: transparent;\n}\n.wmde-markdown img[align='right'] {\n  padding-left: 20px;\n}\n.wmde-markdown img[align='left'] {\n  padding-right: 20px;\n}\n.wmde-markdown .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n.wmde-markdown span.frame {\n  display: block;\n  overflow: hidden;\n}\n.wmde-markdown span.frame > span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid var(--color-border-default);\n}\n.wmde-markdown span.frame span img {\n  display: block;\n  float: left;\n}\n.wmde-markdown span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: var(--color-fg-default);\n}\n.wmde-markdown span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.wmde-markdown span.align-center > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n.wmde-markdown span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n.wmde-markdown span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.wmde-markdown span.align-right > span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n.wmde-markdown span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n.wmde-markdown span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n.wmde-markdown span.float-left span {\n  margin: 13px 0 0;\n}\n.wmde-markdown span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n.wmde-markdown span.float-right > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n.wmde-markdown code,\n.wmde-markdown tt {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 85%;\n  background-color: var(--color-neutral-muted);\n  border-radius: 6px;\n}\n.wmde-markdown code br,\n.wmde-markdown tt br {\n  display: none;\n}\n.wmde-markdown del code {\n  text-decoration: inherit;\n}\n.wmde-markdown pre code {\n  font-size: 100%;\n}\n.wmde-markdown pre > code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n.wmde-markdown pre {\n  font-size: 85%;\n  line-height: 1.45;\n  background-color: var(--color-canvas-subtle);\n  border-radius: 6px;\n}\n.wmde-markdown pre code,\n.wmde-markdown pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n.wmde-markdown pre > code {\n  padding: 16px;\n  overflow: auto;\n  display: block;\n}\n.wmde-markdown pre > code::-webkit-scrollbar {\n  background: transparent;\n  width: 8px;\n  height: 8px;\n}\n.wmde-markdown pre > code::-webkit-scrollbar-thumb {\n  background: var(--color-fg-muted);\n  border-radius: 10px;\n}\n.wmde-markdown .csv-data td,\n.wmde-markdown .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n.wmde-markdown .csv-data .blob-num {\n  padding: 10px 8px 9px;\n  text-align: right;\n  background: var(--color-canvas-default);\n  border: 0;\n}\n.wmde-markdown .csv-data tr {\n  border-top: 0;\n}\n.wmde-markdown .csv-data th {\n  font-weight: 600;\n  background: var(--color-canvas-subtle);\n  border-top: 0;\n}\n.wmde-markdown .footnotes {\n  font-size: 12px;\n  color: var(--color-fg-muted);\n  border-top: 1px solid var(--color-border-default);\n}\n.wmde-markdown .footnotes ol {\n  padding-left: 16px;\n}\n.wmde-markdown .footnotes li {\n  position: relative;\n}\n.wmde-markdown .footnotes li:target::before {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  bottom: -8px;\n  left: -24px;\n  pointer-events: none;\n  content: '';\n  border: 2px solid var(--color-accent-emphasis);\n  border-radius: 6px;\n}\n.wmde-markdown .footnotes li:target {\n  color: var(--color-fg-default);\n}\n.wmde-markdown .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n.wmde-markdown .task-list-item {\n  list-style-type: none;\n}\n.wmde-markdown .task-list-item label {\n  font-weight: 400;\n}\n.wmde-markdown .task-list-item.enabled label {\n  cursor: pointer;\n}\n.wmde-markdown .task-list-item + .wmde-markdown .task-list-item {\n  margin-top: 3px;\n}\n.wmde-markdown .task-list-item .handle {\n  display: none;\n}\n.wmde-markdown .task-list-item-checkbox,\n.wmde-markdown .contains-task-list input[type='checkbox'] {\n  margin: 0 0.2em 0.25em -1.6em;\n  vertical-align: middle;\n}\n.wmde-markdown .contains-task-list:dir(rtl) .task-list-item-checkbox,\n.wmde-markdown .contains-task-list:dir(rtl) input[type='checkbox'] {\n  margin: 0 -1.6em 0.25em 0.2em;\n}\n.wmde-markdown ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n.wmde-markdown pre {\n  position: relative;\n}\n.wmde-markdown pre .copied {\n  visibility: hidden;\n  display: flex;\n  position: absolute;\n  cursor: pointer;\n  color: var(--color-fg-default);\n  top: 6px;\n  right: 6px;\n  border-radius: 5px;\n  background: var(--color-border-default);\n  padding: 6px;\n  font-size: 12px;\n  transition: all 0.3s;\n}\n.wmde-markdown pre .copied .octicon-copy {\n  display: block;\n}\n.wmde-markdown pre .copied .octicon-check {\n  display: none;\n}\n.wmde-markdown pre:hover .copied {\n  visibility: visible;\n}\n.wmde-markdown pre:hover .copied:hover {\n  background: var(--color-prettylights-syntax-entity-tag);\n  color: var(--color-canvas-default);\n}\n.wmde-markdown pre:hover .copied:active,\n.wmde-markdown pre .copied.active {\n  background: var(--color-copied-active-bg);\n  color: var(--color-canvas-default);\n}\n.wmde-markdown pre .active .octicon-copy {\n  display: none;\n}\n.wmde-markdown pre .active .octicon-check {\n  display: block;\n}\n.wmde-markdown .markdown-alert {\n  padding: 0.5rem 1em;\n  color: inherit;\n  margin-bottom: 16px;\n  border-left: 0.25em solid var(--borderColor-default, var(--color-border-default));\n}\n.wmde-markdown .markdown-alert > :last-child {\n  margin-bottom: 0 !important;\n}\n.wmde-markdown .markdown-alert .markdown-alert-title {\n  display: flex;\n  align-items: center;\n  line-height: 1;\n  font-weight: 500;\n  font-size: 14px;\n}\n.wmde-markdown .markdown-alert .markdown-alert-title svg.octicon {\n  margin-right: var(--base-size-8, 8px) !important;\n}\n.wmde-markdown .markdown-alert.markdown-alert-note {\n  border-left-color: var(--borderColor-accent-emphasis, var(--color-accent-emphasis));\n}\n.wmde-markdown .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: var(--fgColor-accent, var(--color-accent-fg));\n}\n.wmde-markdown .markdown-alert.markdown-alert-tip {\n  border-left-color: var(--borderColor-success-emphasis, var(--color-success-emphasis));\n}\n.wmde-markdown .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: var(--fgColor-success, var(--color-success-fg));\n}\n.wmde-markdown .markdown-alert.markdown-alert-important {\n  border-left-color: var(--borderColor-done-emphasis, var(--color-done-emphasis));\n}\n.wmde-markdown .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: var(--fgColor-done, var(--color-done-fg));\n}\n.wmde-markdown .markdown-alert.markdown-alert-warning {\n  border-left-color: var(--borderColor-attention-emphasis, var(--color-attention-emphasis));\n}\n.wmde-markdown .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: var(--fgColor-attention, var(--color-attention-fg));\n}\n.wmde-markdown .markdown-alert.markdown-alert-caution {\n  border-left-color: var(--borderColor-danger-emphasis, var(--color-danger-emphasis));\n}\n.wmde-markdown .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: var(--fgColor-danger, var(--color-danger-fg));\n}\n.wmde-markdown .highlight-line {\n  background-color: var(--color-neutral-muted);\n}\n.wmde-markdown .code-line.line-number::before {\n  display: inline-block;\n  width: 1rem;\n  text-align: right;\n  margin-right: 16px;\n  color: var(--color-fg-subtle);\n  content: attr(line);\n  white-space: nowrap;\n}\n.wmde-markdown .token.comment,\n.wmde-markdown .token.prolog,\n.wmde-markdown .token.doctype,\n.wmde-markdown .token.cdata {\n  color: var(--color-prettylights-syntax-comment);\n}\n.wmde-markdown .token.namespace {\n  opacity: 0.7;\n}\n.wmde-markdown .token.property,\n.wmde-markdown .token.tag,\n.wmde-markdown .token.selector,\n.wmde-markdown .token.constant,\n.wmde-markdown .token.symbol,\n.wmde-markdown .token.deleted {\n  color: var(--color-prettylights-syntax-entity-tag);\n}\n.wmde-markdown .token.maybe-class-name {\n  color: var(--color-prettylights-syntax-variable);\n}\n.wmde-markdown .token.property-access,\n.wmde-markdown .token.operator,\n.wmde-markdown .token.boolean,\n.wmde-markdown .token.number,\n.wmde-markdown .token.selector .token.class,\n.wmde-markdown .token.attr-name,\n.wmde-markdown .token.string,\n.wmde-markdown .token.char,\n.wmde-markdown .token.builtin {\n  color: var(--color-prettylights-syntax-constant);\n}\n.wmde-markdown .token.deleted {\n  color: var(--color-prettylights-syntax-markup-deleted-text);\n}\n.wmde-markdown .code-line .token.deleted {\n  background-color: var(--color-prettylights-syntax-markup-deleted-bg);\n}\n.wmde-markdown .token.inserted {\n  color: var(--color-prettylights-syntax-markup-inserted-text);\n}\n.wmde-markdown .code-line .token.inserted {\n  background-color: var(--color-prettylights-syntax-markup-inserted-bg);\n}\n.wmde-markdown .token.variable {\n  color: var(--color-prettylights-syntax-constant);\n}\n.wmde-markdown .token.entity,\n.wmde-markdown .token.url,\n.wmde-markdown .language-css .token.string,\n.wmde-markdown .style .token.string {\n  color: var(--color-prettylights-syntax-string);\n}\n.wmde-markdown .token.color,\n.wmde-markdown .token.atrule,\n.wmde-markdown .token.attr-value,\n.wmde-markdown .token.function,\n.wmde-markdown .token.class-name {\n  color: var(--color-prettylights-syntax-string);\n}\n.wmde-markdown .token.rule,\n.wmde-markdown .token.regex,\n.wmde-markdown .token.important,\n.wmde-markdown .token.keyword {\n  color: var(--color-prettylights-syntax-keyword);\n}\n.wmde-markdown .token.coord {\n  color: var(--color-prettylights-syntax-meta-diff-range);\n}\n.wmde-markdown .token.important,\n.wmde-markdown .token.bold {\n  font-weight: bold;\n}\n.wmde-markdown .token.italic {\n  font-style: italic;\n}\n.wmde-markdown .token.entity {\n  cursor: help;\n}\n"], "names": [], "mappings": "AAAA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDF;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;;;;;;;;;;AASA;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAGA;;;;;;;;AAOA;;;;;AAIA;;;;AAGA;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;AAOA;;;;AAGA;;;;;;;;;;;;AAWA;;;;;;;;;AAQA;;;;AAKA;;;;;AAMA;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAIA;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;;;AAyCA;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;AAaA;;;;;;;AAWA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAMA;;;;AAGA;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAMA;;;;;AAIA;;;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAUA;;;;AAQA;;;;AAQA;;;;;AAeA;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;AAOA;;;;;AAIA;;;;AAGA;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;;;;;AASA;;;;;AAIA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAKA;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;;AAcA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;AAMA;;;;AAGA;;;;AAQA;;;;AAGA;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAaA;;;;AAMA;;;;AAGA;;;;AAIA;;;;AAGA", "ignoreList": [0]}}]}