{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/highlight.js/styles/github-dark.css"], "sourcesContent": ["pre code.hljs {\n  display: block;\n  overflow-x: auto;\n  padding: 1em\n}\ncode.hljs {\n  padding: 3px 5px\n}\n/*!\n  Theme: GitHub Dark\n  Description: Dark theme as seen on github.com\n  Author: github.com\n  Maintainer: @Hirse\n  Updated: 2021-05-15\n\n  Outdated base version: https://github.com/primer/github-syntax-dark\n  Current colors taken from GitHub's CSS\n*/\n.hljs {\n  color: #c9d1d9;\n  background: #0d1117\n}\n.hljs-doctag,\n.hljs-keyword,\n.hljs-meta .hljs-keyword,\n.hljs-template-tag,\n.hljs-template-variable,\n.hljs-type,\n.hljs-variable.language_ {\n  /* prettylights-syntax-keyword */\n  color: #ff7b72\n}\n.hljs-title,\n.hljs-title.class_,\n.hljs-title.class_.inherited__,\n.hljs-title.function_ {\n  /* prettylights-syntax-entity */\n  color: #d2a8ff\n}\n.hljs-attr,\n.hljs-attribute,\n.hljs-literal,\n.hljs-meta,\n.hljs-number,\n.hljs-operator,\n.hljs-variable,\n.hljs-selector-attr,\n.hljs-selector-class,\n.hljs-selector-id {\n  /* prettylights-syntax-constant */\n  color: #79c0ff\n}\n.hljs-regexp,\n.hljs-string,\n.hljs-meta .hljs-string {\n  /* prettylights-syntax-string */\n  color: #a5d6ff\n}\n.hljs-built_in,\n.hljs-symbol {\n  /* prettylights-syntax-variable */\n  color: #ffa657\n}\n.hljs-comment,\n.hljs-code,\n.hljs-formula {\n  /* prettylights-syntax-comment */\n  color: #8b949e\n}\n.hljs-name,\n.hljs-quote,\n.hljs-selector-tag,\n.hljs-selector-pseudo {\n  /* prettylights-syntax-entity-tag */\n  color: #7ee787\n}\n.hljs-subst {\n  /* prettylights-syntax-storage-modifier-import */\n  color: #c9d1d9\n}\n.hljs-section {\n  /* prettylights-syntax-markup-heading */\n  color: #1f6feb;\n  font-weight: bold\n}\n.hljs-bullet {\n  /* prettylights-syntax-markup-list */\n  color: #f2cc60\n}\n.hljs-emphasis {\n  /* prettylights-syntax-markup-italic */\n  color: #c9d1d9;\n  font-style: italic\n}\n.hljs-strong {\n  /* prettylights-syntax-markup-bold */\n  color: #c9d1d9;\n  font-weight: bold\n}\n.hljs-addition {\n  /* prettylights-syntax-markup-inserted */\n  color: #aff5b4;\n  background-color: #033a16\n}\n.hljs-deletion {\n  /* prettylights-syntax-markup-deleted */\n  color: #ffdcd7;\n  background-color: #67060c\n}\n.hljs-char.escape_,\n.hljs-link,\n.hljs-params,\n.hljs-property,\n.hljs-punctuation,\n.hljs-tag {\n  /* purposely ignored */\n  \n}"], "names": [], "mappings": "AAAA;;;;;;AAKA;;;;AAaA;;;;;AAIA;;;;AAUA;;;;AAOA;;;;AAaA;;;;AAMA;;;;AAKA;;;;AAMA;;;;AAOA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA", "ignoreList": [0]}}]}